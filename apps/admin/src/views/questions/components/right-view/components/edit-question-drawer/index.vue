<script setup lang="ts">
import type { TransformToVoQuestionData } from '@sa/utils'
import CKEditor from '@sa/components/common/ck-editor/index.vue'
import type { FormInst } from 'naive-ui'
import QuestionItemContainer from '@sa/components/common/questions/question-item-container.vue'

defineOptions({
  name: 'EditQuestionDrawer',
})

// 定义props
const props = defineProps<{
  question: TransformToVoQuestionData
}>()

// 定义emits
const emit = defineEmits<{
  save: [question: TransformToVoQuestionData]
  cancel: []
}>()

// 抽屉显示状态
const visible = defineModel<boolean>('visible', {
  default: false,
})

// 监听抽屉关闭
function handleClose() {
  visible.value = false
  emit('cancel')
}

const formRef = ref<FormInst | null>(null)

const dynamicForm = reactive({
  name: '',
  hobbies: [{ hobby: '' }],
})
// 创建本地副本用于编辑
const localQuestion = ref<TransformToVoQuestionData>()
// 保存题目
function handleSave() {
  if (!localQuestion.value) {
    window.$message?.error('题目数据不能为空')
    return
  }

  // 基本验证
  if (!localQuestion.value.title?.trim()) {
    window.$message?.error('题干不能为空')
    return
  }

  if (!localQuestion.value.analysis?.trim()) {
    window.$message?.error('答案解析不能为空')
    return
  }

  // 验证选项内容是否为空（适用于有选项的题型）
  if (localQuestion.value.options && localQuestion.value.options.length > 0) {
    const emptyOptions = localQuestion.value.options.filter(option => !option.label?.trim())
    if (emptyOptions.length > 0) {
      window.$message?.error('选项内容不能为空，请完善所有选项')
      return
    }
  }

  // 发送编辑后的题目数据
  emit('save', localQuestion.value)
  visible.value = false
  window.$message?.success('题目保存成功')
}
// 添加选项功能
function handleAddOption() {
  if (!localQuestion.value || !localQuestion.value.options) {
    return
  }

  // 获取下一个选项标识符
  const nextOptionValue = getNextOptionValue(localQuestion.value.options)

  // 添加新选项
  const newOption = {
    label: '',
    value: nextOptionValue,
  }

  localQuestion.value.options.push(newOption)
}

// 获取下一个选项标识符（A, B, C, D, E...）
function getNextOptionValue(options: Array<{ label: string, value: string }>): string {
  const existingValues = options.map(option => option.value).sort()
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'

  for (let i = 0; i < alphabet.length; i++) {
    const letter = alphabet[i]
    if (!existingValues.includes(letter)) {
      return letter
    }
  }

  // 如果超过26个选项，使用数字
  return String(options.length + 1)
}

// 监听 props.question 变化，同步到本地副本
watch(() => props.question, (newQuestion) => {
  if (newQuestion) {
    localQuestion.value = JSON.parse(JSON.stringify(newQuestion))
  }
}, { immediate: true, deep: true })
</script>

<template>
  <NDrawer v-model:show="visible" :width="800" placement="right" :auto-focus="false" :trap-focus="false">
    <NDrawerContent :title="`编辑-${localQuestion!.typeText}`" closable :native-scrollbar="false" @close="handleClose">
      <NForm ref="formRef" :model="dynamicForm" label-placement="left" label-width="70px">
        <NFormItem label="题干" :rules="[{ required: true, message: '请输入姓名' }]">
          <CKEditor v-model:editor-value="localQuestion!.title" />
        </NFormItem>
        <QuestionItemContainer v-model:item-info="localQuestion" type="edit" :show-question-stem="false" />

        <!-- 添加选项按钮 -->
        <div v-if="localQuestion && localQuestion.options" class="mb-4">
          <NButton
            type="primary"
            dashed
            class="w-full"
            @click="handleAddOption"
          >
            <template #icon>
              <NIcon>
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              </NIcon>
            </template>
            添加选项
          </NButton>
        </div>

        <NFormItem label="答案解析" :rules="[{ required: true, message: '请输入答案解析' }]">
          <CKEditor v-model:editor-value="localQuestion!.analysis" />
        </NFormItem>
      </NForm>

      <!-- <div class="test w-500px">
        <CKEditor v-for="item in 10" :key="item" class="mb-20px" />
      </div> -->
      <!-- 抽屉底部操作按钮 -->
      <template #footer>
        <div class="flex justify-end gap-3">
          <NButton @click="handleClose">
            取消
          </NButton>
          <NButton type="primary" @click="handleSave">
            保存
          </NButton>
        </div>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
/* 自定义样式 */
</style>
